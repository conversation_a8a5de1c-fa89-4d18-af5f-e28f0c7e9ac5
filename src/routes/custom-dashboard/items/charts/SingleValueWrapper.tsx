/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import NumberFormat from '../../../../utils/number-format';
import { getLatestDisplayValue, getSparklineChartProps } from '../../../summary/insights/utils/helpers';
import { HistoricalUtrs } from '../../../../api/insights';
import Chart from 'react-google-charts';
import { ChartType } from '../../../summary/insights/utils/constants';
import { ChartDataTable } from '../../../../utils/charts';
import { Button } from 'reactstrap';
import { isDefined } from '../../../../utils';
import {
  ChartSubType,
  GridDashboardChartItem,
  InsightDashboardItemType,
  SubType,
} from '../../../../types/insight-custom-dashboard';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { DISABLED_TARGET_BASELINE_ADDING_TOOLTIP } from '../../utils';
import { TakenFromText } from './common/TakenFromText';
import { QuestionReference } from '../../question-reference/QuestionReference';
import { SurveyActionData } from '../../../../model/surveyData';
import { isNumeric } from '@utils/number';
import { getTextColor } from './multi-utrs-chart/utils';

interface SingleValueWrapperProps {
  subType: SubType;
  utrData: HistoricalUtrs | undefined;
  chartData: ChartDataTable;
  metricUnitText: string;
  selectedColumnCode: string | undefined;
  isRestated: boolean;
  handleSetTarget?: () => void;
  readOnly?: boolean;
  unitText?: GridDashboardChartItem['unitText'];
  altUnitText?: string;
  haveTextsSameColor: boolean;
  hideQuestionReference: boolean;
  initiativeId: string;
  survey?: Pick<SurveyActionData, '_id' | 'fragmentUniversalTrackerValues'>;
  type: InsightDashboardItemType.Chart | InsightDashboardItemType.SDGTracker;
}

/** @deprecated in favour of MultiUtrsChartWidget */
export const SingleValueWrapper = (props: SingleValueWrapperProps) => {
  const {
    subType,
    utrData,
    chartData,
    metricUnitText,
    selectedColumnCode,
    isRestated,
    handleSetTarget,
    readOnly = false,
    unitText,
    altUnitText,
    haveTextsSameColor = false,
    hideQuestionReference = false,
    initiativeId,
    survey,
  } = props;

  const { actual, target, effectiveDate, period } = getLatestDisplayValue(utrData?.utrvs, selectedColumnCode);
  const isSuffix = metricUnitText === '%';

  const textColor = getTextColor({
    actualValue: actual,
    targetValue: target,
    targetDirection: utrData?.utr.targetDirection,
  });

  return (
    <div className='w-100 h-100 d-flex flex-column justify-content-center'>
      <div className='flex-fill d-flex flex-column justify-content-center'>
        {isNumeric(actual) ? (
          <NumberFormat
            className={`h1 m-0 text-center text-truncate ${ haveTextsSameColor ? textColor : 'text-ThemeHeadingLight'}`}
            value={actual}
            suffix={isSuffix ? '%' : ''}
            maximumFractionDigits={2}
            hasTooltip
          />
        ) : (
          <div className='h1 m-0 text-center text-ThemeHeadingLight'>-</div>
        )}
        {!isSuffix ? <div className='text-sm text-center text-ThemeTextLight'>{metricUnitText}</div> : null}
        {unitText ? <div className='text-sm text-center text-ThemeTextLight'>{unitText}</div> : null}
        {altUnitText ? (
            <div className={`text-sm text-center ${textColor}`}>{altUnitText}</div>
          ) : null}
        {isDefined(target) ? (
          <div className={`text-xs text-center ${textColor}`}>
            Target: {target} {metricUnitText} {altUnitText}
          </div>
        ) : !readOnly ? (
          <SimpleTooltip text={!handleSetTarget ? DISABLED_TARGET_BASELINE_ADDING_TOOLTIP : ''} className='text-center'>
            <Button
              color='link'
              disabled={!handleSetTarget}
              onClick={handleSetTarget}
              className='text-xs text-ThemePrimary'
            >
              Set target
            </Button>
          </SimpleTooltip>
        ) : null}
      </div>
      {subType === ChartSubType.SparkLine ? (
        <div style={{ height: '40%' }}>
          {chartData.length ? (
            <Chart
              height={'100%'}
              chartType={ChartType.LineChart}
              data={chartData}
              options={getSparklineChartProps({ isRestated })}
            />
          ) : (
            <p className='text-sm text-medium'>No available data</p>
          )}
        </div>
      ) : null}
      <TakenFromText effectiveDate={effectiveDate} period={period} />
      {utrData?.utr && !hideQuestionReference ? (
        <QuestionReference
          initiativeId={initiativeId}
          utrsCodes={[utrData.utr.code]}
          utrsData={[utrData]}
          survey={survey}
          btnClass='text-left'
        />
      ) : null}
    </div>
  );
};
