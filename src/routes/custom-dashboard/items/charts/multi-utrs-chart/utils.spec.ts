import { describe, it, expect } from 'vitest';
import { getTextColor } from './utils';

describe('getTextColor', () => {
  describe('when actualValue or targetValue are undefined/null', () => {
    it('should return default color when actualValue is undefined', () => {
      const result = getTextColor({
        actualValue: undefined,
        targetValue: 100,
        targetDirection: 'increase',
      });
      expect(result).toBe('text-ThemeTextMedium');
    });

    it('should return default color when targetValue is undefined', () => {
      const result = getTextColor({
        actualValue: 50,
        targetValue: undefined,
        targetDirection: 'increase',
      });
      expect(result).toBe('text-ThemeTextMedium');
    });

    it('should return default color when both values are undefined', () => {
      const result = getTextColor({
        actualValue: undefined,
        targetValue: undefined,
        targetDirection: 'increase',
      });
      expect(result).toBe('text-ThemeTextMedium');
    });

    it('should return default color when actualValue is null', () => {
      const result = getTextColor({
        actualValue: null as any,
        targetValue: 100,
        targetDirection: 'increase',
      });
      expect(result).toBe('text-ThemeTextMedium');
    });

    it('should return default color when targetValue is null', () => {
      const result = getTextColor({
        actualValue: 50,
        targetValue: null as any,
        targetDirection: 'increase',
      });
      expect(result).toBe('text-ThemeTextMedium');
    });

    it('should return default color when actualValue is empty string', () => {
      const result = getTextColor({
        actualValue: '',
        targetValue: 100,
        targetDirection: 'increase',
      });
      expect(result).toBe('text-ThemeTextMedium');
    });

    it('should return default color when targetValue is empty string', () => {
      const result = getTextColor({
        actualValue: 50,
        targetValue: '',
        targetDirection: 'increase',
      });
      expect(result).toBe('text-ThemeTextMedium');
    });
  });

  describe('when targetDirection is undefined/not provided', () => {
    it('should return success color when actualValue > targetValue (numbers)', () => {
      const result = getTextColor({
        actualValue: 150,
        targetValue: 100,
      });
      expect(result).toBe('text-ThemeSuccessMedium');
    });

    it('should return warning color when actualValue < targetValue (numbers)', () => {
      const result = getTextColor({
        actualValue: 50,
        targetValue: 100,
      });
      expect(result).toBe('text-ThemeWarningMedium');
    });

    it('should return warning color when actualValue equals targetValue (numbers)', () => {
      const result = getTextColor({
        actualValue: 100,
        targetValue: 100,
      });
      expect(result).toBe('text-ThemeWarningMedium');
    });

    it('should return success color when actualValue > targetValue (strings)', () => {
      const result = getTextColor({
        actualValue: '150',
        targetValue: '100',
      });
      expect(result).toBe('text-ThemeSuccessMedium');
    });

    it('should return warning color when actualValue < targetValue (strings)', () => {
      const result = getTextColor({
        actualValue: '50',
        targetValue: '100',
      });
      expect(result).toBe('text-ThemeWarningMedium');
    });

    it('should return success color when actualValue > targetValue (mixed types)', () => {
      const result = getTextColor({
        actualValue: 150,
        targetValue: '100',
      });
      expect(result).toBe('text-ThemeSuccessMedium');
    });

    it('should return warning color when actualValue < targetValue (mixed types)', () => {
      const result = getTextColor({
        actualValue: '50',
        targetValue: 100,
      });
      expect(result).toBe('text-ThemeWarningMedium');
    });
  });

  describe('when targetDirection is "increase"', () => {
    it('should return success color when actualValue > targetValue', () => {
      const result = getTextColor({
        actualValue: 150,
        targetValue: 100,
        targetDirection: 'increase',
      });
      expect(result).toBe('text-ThemeSuccessMedium');
    });

    it('should return warning color when actualValue < targetValue', () => {
      const result = getTextColor({
        actualValue: 50,
        targetValue: 100,
        targetDirection: 'increase',
      });
      expect(result).toBe('text-ThemeWarningMedium');
    });

    it('should return warning color when actualValue equals targetValue', () => {
      const result = getTextColor({
        actualValue: 100,
        targetValue: 100,
        targetDirection: 'increase',
      });
      expect(result).toBe('text-ThemeWarningMedium');
    });

    it('should work with string values', () => {
      const result = getTextColor({
        actualValue: '150',
        targetValue: '100',
        targetDirection: 'increase',
      });
      expect(result).toBe('text-ThemeSuccessMedium');
    });

    it('should work with mixed types', () => {
      const result = getTextColor({
        actualValue: '50',
        targetValue: 100,
        targetDirection: 'increase',
      });
      expect(result).toBe('text-ThemeWarningMedium');
    });
  });

  describe('when targetDirection is "decrease"', () => {
    it('should return success color when actualValue < targetValue', () => {
      const result = getTextColor({
        actualValue: 50,
        targetValue: 100,
        targetDirection: 'decrease',
      });
      expect(result).toBe('text-ThemeSuccessMedium');
    });

    it('should return warning color when actualValue > targetValue', () => {
      const result = getTextColor({
        actualValue: 150,
        targetValue: 100,
        targetDirection: 'decrease',
      });
      expect(result).toBe('text-ThemeWarningMedium');
    });

    it('should return warning color when actualValue equals targetValue', () => {
      const result = getTextColor({
        actualValue: 100,
        targetValue: 100,
        targetDirection: 'decrease',
      });
      expect(result).toBe('text-ThemeWarningMedium');
    });

    it('should work with string values', () => {
      const result = getTextColor({
        actualValue: '50',
        targetValue: '100',
        targetDirection: 'decrease',
      });
      expect(result).toBe('text-ThemeSuccessMedium');
    });

    it('should work with mixed types', () => {
      const result = getTextColor({
        actualValue: 150,
        targetValue: '100',
        targetDirection: 'decrease',
      });
      expect(result).toBe('text-ThemeWarningMedium');
    });
  });

  describe('when targetDirection is unsupported', () => {
    it('should return default color for unsupported direction', () => {
      const result = getTextColor({
        actualValue: 150,
        targetValue: 100,
        targetDirection: 'maintain',
      });
      expect(result).toBe('text-ThemeTextMedium');
    });

    it('should return default color for empty string direction', () => {
      const result = getTextColor({
        actualValue: 150,
        targetValue: 100,
        targetDirection: '',
      });
      expect(result).toBe('text-ThemeSuccessMedium');
    });

    it('should return default color for random string direction', () => {
      const result = getTextColor({
        actualValue: 150,
        targetValue: 100,
        targetDirection: 'random',
      });
      expect(result).toBe('text-ThemeTextMedium');
    });
  });

  describe('edge cases with falsy values', () => {
    // Note: The function treats 0 and empty string as falsy values and returns default color
    it('should return default color for zero actualValue', () => {
      const result = getTextColor({
        actualValue: 0,
        targetValue: 100,
      });
      expect(result).toBe('text-ThemeTextMedium');
    });

    it('should return default color for zero targetValue', () => {
      const result = getTextColor({
        actualValue: 50,
        targetValue: 0,
      });
      expect(result).toBe('text-ThemeTextMedium');
    });

    it('should return default color for both zero values', () => {
      const result = getTextColor({
        actualValue: 0,
        targetValue: 0,
      });
      expect(result).toBe('text-ThemeTextMedium');
    });

    it('should handle negative values without direction', () => {
      const result = getTextColor({
        actualValue: -50,
        targetValue: -100,
      });
      expect(result).toBe('text-ThemeSuccessMedium');
    });

    it('should handle decimal values without direction', () => {
      const result = getTextColor({
        actualValue: 50.5,
        targetValue: 50.2,
      });
      expect(result).toBe('text-ThemeSuccessMedium');
    });

    it('should handle string decimal values without direction', () => {
      const result = getTextColor({
        actualValue: '50.5',
        targetValue: '50.2',
      });
      expect(result).toBe('text-ThemeSuccessMedium');
    });

    it('should return default color for zero actualValue with increase direction', () => {
      const result = getTextColor({
        actualValue: 0,
        targetValue: 100,
        targetDirection: 'increase',
      });
      expect(result).toBe('text-ThemeTextMedium');
    });

    it('should return default color for zero actualValue with decrease direction', () => {
      const result = getTextColor({
        actualValue: 0,
        targetValue: 100,
        targetDirection: 'decrease',
      });
      expect(result).toBe('text-ThemeTextMedium');
    });

    it('should return default color for zero targetValue with direction', () => {
      const result = getTextColor({
        actualValue: 50,
        targetValue: 0,
        targetDirection: 'increase',
      });
      expect(result).toBe('text-ThemeTextMedium');
    });

    it('should handle negative values with increase direction', () => {
      const result = getTextColor({
        actualValue: -50,
        targetValue: -100,
        targetDirection: 'increase',
      });
      expect(result).toBe('text-ThemeSuccessMedium');
    });

    it('should handle negative values with decrease direction', () => {
      const result = getTextColor({
        actualValue: -50,
        targetValue: -100,
        targetDirection: 'decrease',
      });
      expect(result).toBe('text-ThemeWarningMedium');
    });

    it('should handle very large numbers', () => {
      const result = getTextColor({
        actualValue: 1000000,
        targetValue: 999999,
        targetDirection: 'increase',
      });
      expect(result).toBe('text-ThemeSuccessMedium');
    });

    it('should handle very small decimal differences', () => {
      const result = getTextColor({
        actualValue: 0.0001,
        targetValue: 0.0002,
        targetDirection: 'increase',
      });
      expect(result).toBe('text-ThemeWarningMedium');
    });
  });

  describe('comprehensive test cases', () => {
    const testCases = [
      {
        name: 'actualValue > targetValue, no direction',
        input: { actualValue: 150, targetValue: 100 },
        expected: 'text-ThemeSuccessMedium',
      },
      {
        name: 'actualValue < targetValue, no direction',
        input: { actualValue: 50, targetValue: 100 },
        expected: 'text-ThemeWarningMedium',
      },
      {
        name: 'actualValue > targetValue, increase direction',
        input: { actualValue: 150, targetValue: 100, targetDirection: 'increase' },
        expected: 'text-ThemeSuccessMedium',
      },
      {
        name: 'actualValue < targetValue, increase direction',
        input: { actualValue: 50, targetValue: 100, targetDirection: 'increase' },
        expected: 'text-ThemeWarningMedium',
      },
      {
        name: 'actualValue > targetValue, decrease direction',
        input: { actualValue: 150, targetValue: 100, targetDirection: 'decrease' },
        expected: 'text-ThemeWarningMedium',
      },
      {
        name: 'actualValue < targetValue, decrease direction',
        input: { actualValue: 50, targetValue: 100, targetDirection: 'decrease' },
        expected: 'text-ThemeSuccessMedium',
      },
      {
        name: 'undefined actualValue',
        input: { actualValue: undefined, targetValue: 100, targetDirection: 'increase' },
        expected: 'text-ThemeTextMedium',
      },
      {
        name: 'undefined targetValue',
        input: { actualValue: 100, targetValue: undefined, targetDirection: 'increase' },
        expected: 'text-ThemeTextMedium',
      },
      {
        name: 'unsupported direction',
        input: { actualValue: 150, targetValue: 100, targetDirection: 'maintain' },
        expected: 'text-ThemeTextMedium',
      },
    ];

    testCases.forEach(({ name, input, expected }) => {
      it(`should return ${expected} for ${name}`, () => {
        expect(getTextColor(input)).toBe(expected);
      });
    });
  });
});