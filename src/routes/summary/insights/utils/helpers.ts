/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

import { ReportData } from '@g17eco/types/reportData';
import { ChartType, InsightPage } from './constants';
import { UniversalTrackerModalServiceUtrv } from '@reducers/universal-tracker-modal';
import { getChartColors, getValueWithFallback, sortUtrvByDate } from '@utils/charts';
import { UtrvType } from '@constants/status';
import variables from '../../../../css/variables.module.scss';

export const getDataAsNumberTableSum = (reportData: ReportData[] | undefined, utrCode: string, tableColumn?: string): number => {
  if (!reportData) {
    return 0;
  }
  const utrv = reportData.find(d => d.universalTracker.code === utrCode);
  const sum = utrv?.valueData?.table?.reduce((acc, row) => {
    const col = row.find(col => col.code === tableColumn);
    return acc + Number(col?.value ?? 0);
  }, 0);
  return sum ?? 0;
}

interface GetDataAsPercentageTableSum {
  reportData: ReportData[] | undefined;
  utrCode: string;
  tableColumn: string;
  percentageColumn: string;
}
export const getDataAsPercentageTableSum = ({reportData, utrCode, tableColumn, percentageColumn}: GetDataAsPercentageTableSum): number => {
  if (!reportData) {
    return 0;
  }
  const utrv = reportData.find(d => d.universalTracker.code === utrCode);
  const sum = utrv?.valueData?.table?.reduce((acc, row) => {
    const col = row.find(col => col.code === tableColumn);
    const percentageCol = row.find(col => col.code === percentageColumn);
    const multipliedValue = Number(col?.value ?? 0) * (Number(percentageCol?.value ?? 0) / 100)
    return acc + multipliedValue;
  }, 0);
  return sum ?? 0;
}

export const getInsightPageOptions = () => {

  const options = [
    {
      value: InsightPage.Environmental,
      label: 'Environmental'
    },
    {
      value: InsightPage.Social,
      label: 'Social'
    },
    {
      value: InsightPage.Governance,
      label: 'Governance'
    }
  ];

  return [
    {
      value: InsightPage.Overview,
      label: 'Overview'
    },
    ...options,
  ];
};

export const getValidPage = (page: string | undefined): InsightPage => {
  if (page && Object.values(InsightPage).includes(page as InsightPage)) {
    return page as InsightPage;
  }
  return InsightPage.Overview;
};

export const isInsightLayoutPage = (page: string): page is InsightPage => {
  switch (page) {
    case InsightPage.Environmental:
    case InsightPage.Social:
    case InsightPage.Governance:
      return true;
    default:
      return false;
  }
}

// @deprecated this function should be removed after SingleValueWrapper is cleaned up
export const getLatestDisplayValue = (
  utrvs: UniversalTrackerModalServiceUtrv[] | undefined,
  columnCode?: string
) => {
  if (!utrvs || utrvs.length <= 0) {
    return {
      actual: undefined,
      target: undefined,
      effectiveDate: '',
    };
  }
  const sortedUtrvs = utrvs.slice().sort(sortUtrvByDate).reverse();
  const latestActual = sortedUtrvs.find(utrv => utrv.type === UtrvType.Actual);
  const latestTarget = sortedUtrvs.find(utrv => utrv.type === UtrvType.Target);

  return {
    actual: latestActual ? getValueWithFallback(latestActual, columnCode, { fallback: undefined }) : undefined,
    target: latestTarget ? getValueWithFallback(latestTarget, columnCode, { fallback: undefined }) : undefined,
    effectiveDate: latestActual?.effectiveDate || latestTarget?.effectiveDate || '',
    period: latestActual?.period || latestTarget?.period || '',
  };
};

export const getSparklineChartProps = ({ isRestated, isMultiple }: { isRestated: boolean; isMultiple?: boolean }) => ({
  legend: {},
  chartType: ChartType.LineChart,
  interpolateNulls: true,
  pointSize: 5,
  colors: getChartColors(isRestated, isMultiple),
  lineWidth: 2,
  chartArea: {
    left: 10,
    top: 10,
    bottom: 10,
    right: 10,
  },
  backgroundColor: variables.BgToolbar,
  hAxis: {
    baselineColor: 'none',
    ticks: [],
  },
  vAxis: {
    baselineColor: 'transparent',
    ticks: [],
    minValue: 0,
  },
  series: {
    0: { type: 'line' },
    1: { type: 'line' },
  },
});
