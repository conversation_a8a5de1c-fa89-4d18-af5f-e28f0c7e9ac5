/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import { DATE, getUtc, isBefore } from '../utils/date';
import { UniversalTrackerValuePlain } from '../types/surveyScope';
import { UtrvType } from '../constants/status';
import { UtrValueType } from '../types/universalTracker';
import { UniversalTrackerModalServiceUtrv } from '../reducers/universal-tracker-modal';
import { UtrvAssuranceStatus } from '../types/universalTrackerValue';
import variables from '../css/variables.module.scss';
import { isNumericString } from './string';
import { isNumeric } from './number';
import { isDefined } from '.';
import { TOTAL_OPTION } from '@components/utr-modal/ColumnFilter';

const POINT_SIZE_0 = 'point { size: 0 }';
const IS_SOLID_LINE = true;
export const CHART_SPACING_RATIO = 1.2;

const NUMBER_METRICS: NumberMetric[] = [UtrvType.Target, UtrvType.Baseline, UtrvType.Actual];
const NUMBER_METRIC_LABELS = ['Target', 'Baseline', 'Actual'];
interface ChartMetric {
  type: string;
  label?: string;
  role?: string;
}

type NumberMetric = 'baseline' | 'actual' | 'target';

type ChartNumberValueObj = {
  [key in NumberMetric]: number | undefined;
};

type ChartValueObj = ChartNumberValueObj & {
  date: Date;
  baselineTooltip?: string;
  actualTooltip?: string;
  targetTooltip?: string;
};

type ChartNumber = number | undefined;
type ChartCertainty = boolean | undefined;
type ChartStyle = string | null;
type ChartTooltip = string | undefined;
type ChartLine = [ChartNumber, ChartCertainty, ChartStyle, ChartTooltip];
type ChartValue = [Date, ...ChartLine, ...ChartLine, ...ChartLine];

type DefaultTypeOptions = { isMultiRowsTable?: boolean; fallback?: number | string };
type NumericTypeOptions = Pick<DefaultTypeOptions, 'isMultiRowsTable'>;

export type ChartDataTable = (ChartMetric[] | ChartValue)[];

interface ChartDataObj {
  [key: string]: ChartValueObj | undefined;
}

const getChartLineColumns = (label: string) => [
  {
    type: 'number',
    label,
  },
  {
    type: 'boolean',
    role: 'certainty',
  },
  {
    type: 'string',
    role: 'style',
  },
  {
    type: 'string',
    role: 'tooltip',
  },
];


/**
 * Returns an array of chart line values based on the input parameters.
 *
 * @param {number | undefined} value - The value to be included in the chart line values.
 * @param {boolean} isSolidLine - Determines whether the chart line should be solid or not. Defaults to IS_SOLID_LINE.
 * @param {null | string} tooltip - The tooltip to be displayed for the chart line. Defaults can only be null.
 * @returns {Array<any>} - An array containing the value, isSolidLine flag, style, and tooltip.
 */

const getChartLineValues = (value: number | undefined, isSolidLine: boolean = IS_SOLID_LINE, tooltip: null | string = null): Array<any> => {
  const style = isSolidLine ? null : POINT_SIZE_0;
  // Every other columns except value column cannot be undefined
  // Because we use undefined to determine the missing value columns
  return [value, isSolidLine, style, tooltip];
};

export const CHART_METRICS: ChartMetric[] = [
  {
    type: 'date',
    label: 'Date',
  },
  ...NUMBER_METRIC_LABELS.map((label: string) => getChartLineColumns(label)).flat(),
];

export const getNumericValue = (
  universalTrackerValue: Pick<UniversalTrackerValuePlain, 'valueType' | 'valueData' | 'value'>,
  columnCode?: string,
  { isMultiRowsTable }: NumericTypeOptions = {}
) => {
  switch (universalTrackerValue.valueType) {
    case UtrValueType.Table: {
      const tableData = universalTrackerValue.valueData?.table;
      if (!tableData) {
        return 0;
      }

      if (isMultiRowsTable) {
        return tableData.reduce((sum, row) => {
          const columnData = row.find((col) => col.code === columnCode);
          return sum + Number(columnData?.value);
        }, 0);
      }

      const columnData = tableData[0]?.find((col) => col.code === columnCode);
      return Number(columnData?.value);
    }
    case UtrValueType.NumericValueList: {
      if (!columnCode || columnCode === TOTAL_OPTION.value) {
        return Number(universalTrackerValue.value);
      }
      return Number(universalTrackerValue.valueData?.data?.[columnCode]);
    }
    case UtrValueType.ValueList: {
      const data = universalTrackerValue.valueData?.data;
      if (isNumeric(data)) {
        return Number(data);
      }
      // treat yes/no value list only for the Table chart.
      return data === 'yes' ? 1 : 0;
    }
    case UtrValueType.Number:
    case UtrValueType.Percentage:
    default:
      // Unit tests have valueType=undefined
      return Number(universalTrackerValue.value);
  }
};

export const getChartMaxValue = (chartData?: unknown[][]) => {
  if (!chartData) {
    return undefined;
  }

  const data = chartData.slice(1);

  const maxValue = data.reduce((acc: number | undefined, row: any[]) => {
    const maxRowValue = row.reduce((accRow: number | undefined, cell: any) => {
      if (!isDefined(cell) || !isNumeric(cell)) {
        return accRow;
      }

      return getMaxValue(cell, accRow);
    }, undefined);

    return getMaxValue(maxRowValue, acc);
  }, undefined);

  return maxValue ? maxValue * CHART_SPACING_RATIO : undefined;
};

const getMaxValue = (currentValue: number | undefined, maxValue: number | undefined) => {
  if (currentValue === undefined) {
    return maxValue;
  }
  if (maxValue === undefined) {
    return currentValue;
  }
  return currentValue > maxValue ? currentValue : maxValue;
};

export const getValueWithFallback = (
  universalTrackerValue: Pick<UniversalTrackerValuePlain, 'valueType' | 'valueData' | 'value'>,
  columnCode?: string,
  { isMultiRowsTable, fallback = '' }: DefaultTypeOptions = {}
) => {
  switch (universalTrackerValue.valueType) {
    case UtrValueType.Table: {
      const tableData = universalTrackerValue.valueData?.table;
      if (!tableData) {
        return fallback;
      }

      if (isMultiRowsTable) {
        return tableData.reduce((sum, row) => {
          const columnData = row.find((col) => col.code === columnCode);
          if (!isNumericString(columnData?.value)) {
            return sum;
          }
          if (!sum) {
            return Number(columnData?.value);
          }
          return sum + Number(columnData?.value);
        }, undefined as number | undefined);
      }

      const columnData = tableData[0]?.find((col) => col.code === columnCode);
      return Number(columnData?.value);
    }
    case UtrValueType.NumericValueList: {
      if (!columnCode || columnCode === TOTAL_OPTION.value) {
        return universalTrackerValue.value ?? fallback;
      }
      return Number(universalTrackerValue.valueData?.data?.[columnCode]);
    }
    case UtrValueType.ValueList: {
      const data = universalTrackerValue.valueData?.data;
      if (isNumeric(data)) {
        return Number(data);
      }
      // treat yes/no value list only for the Table chart.
      return data === 'yes' ? 1 : 0;
    }
    case UtrValueType.Number:
    case UtrValueType.Percentage:
    default:
      // Unit tests have valueType=undefined
      return universalTrackerValue.value ?? fallback;
  }
};

const addPrevData = (prevData: ChartValueObj | undefined, chartData: ChartDataTable) => {
  if (!prevData) {
    return;
  }

  const { date, baseline, actual, target } = prevData;
  chartData.push([
    new Date(date),
    ...getChartLineValues(target, IS_SOLID_LINE, prevData.targetTooltip),
    ...getChartLineValues(baseline, IS_SOLID_LINE, prevData.baselineTooltip),
    ...getChartLineValues(actual, IS_SOLID_LINE, prevData.actualTooltip),
  ] as ChartValue);
};

type SortableUtrv = Pick<UniversalTrackerValuePlain, 'effectiveDate'>;
export const sortUtrvByDate = <T extends SortableUtrv = SortableUtrv>(a: T, b: T) => {
  // "2018-11-21T07:10:00.000Z" vs "2018-11-21T07:00:00.000Z" etc
  if (a.effectiveDate > b.effectiveDate) {
    return 1;
  }
  return a.effectiveDate < b.effectiveDate ? -1 : 0;
};

const getTooltip = (utrv: ChartUtrv, columnCode?: string) => {
  const { effectiveDate, assuranceStatus, type } = utrv;
  const ymd = getUtc(effectiveDate).format(DATE.MONTH_YEAR_SHORT);
  // value is used for chart, therefore must be a number
  const value = getNumericValue(utrv, columnCode);
  if (assuranceStatus === UtrvAssuranceStatus.Restated) {
    // return [ymd, type, value]
    return `${ymd}\n${type}: ${value}\nrestatement of previously\nassured data`;
  }
  return `${ymd}\n${type}: ${value}`;
}

export const isAssuranceRestated = (utrvs: UniversalTrackerModalServiceUtrv[] = []) => {
  return [...utrvs].sort(sortUtrvByDate).pop()?.assuranceStatus === UtrvAssuranceStatus.Restated || false;
}

export const getChartColors = (isRestated: boolean = false, isMultiple = false) => {
  // Multiple utrs chart does not support target/baseline as well as restarted utrv.
  if (isMultiple) {
    return [variables.ChartActual];
  }

  return [
    variables.ChartTarget,
    variables.ChartBaseline,
    isRestated ? variables.ChartRestated : variables.ChartActual,
  ];
}


type ChartUtrv = Pick<UniversalTrackerModalServiceUtrv,
  | 'value'
  | 'valueType'
  | 'valueData'
  | 'effectiveDate'
  | 'period'
  | 'type'
  | 'status'
  | 'assuranceStatus'
>

export const getChartDataByValues = (valuesByDate: ChartUtrv[] | undefined, columnCode?: string) => {
  if (!valuesByDate) {
    return [];
  }

  const totalCount = valuesByDate.length;
  if (totalCount <= 0) {
    return [];
  }

  const chartData: ChartDataTable = [CHART_METRICS];

  const accumulatedValues: ChartNumberValueObj = {
    baseline: undefined,
    actual: undefined,
    target: undefined,
  };

  const isLastRun = (i: number) => i === totalCount;

  const chartDataObj: ChartDataObj = {};
  let currentYmd = '';
  let i = 0;
  let dateObj;

  const sortedValues = valuesByDate.slice().sort(sortUtrvByDate);

  for (const universalTrackerValue of sortedValues) {
    i++;

    dateObj = getUtc(universalTrackerValue.effectiveDate);
    const ymd = dateObj.format('YYYY-MM-DD');

    let values: ChartValueObj | undefined = chartDataObj[ymd];

    // New data or last entry
    if (!values) {
      addPrevData(chartDataObj[currentYmd], chartData);
      values = {
        date: dateObj.toDate(),
        baseline: undefined,
        actual: undefined,
        target: undefined,
        baselineTooltip: undefined,
        actualTooltip: undefined,
        targetTooltip: undefined,
      };
      chartDataObj[ymd] = values;
      currentYmd = ymd;
    }

    // value is used for chart, therefore must be a number
    const value = getNumericValue(universalTrackerValue, columnCode);
    const type = universalTrackerValue.type as NumberMetric;

    values[type] = value;
    values[`${type}Tooltip`] = getTooltip(universalTrackerValue, columnCode);
    accumulatedValues[type] = value;

    if (isLastRun(i)) {
      addPrevData(chartDataObj[currentYmd], chartData);
    }
  }

  const [, ...rows] = chartData;
  const firstRow = rows[0];
  const lastRow = rows[rows.length - 1];

  // order of output row follows this order [date, target, baseline, actual]
  // target, baseline and actual is an array: [value, lineStyle, pointStyle, tooltip]
  // output row is a flat array concatenating date, target, baseline and actual
  for (let columnIndex = 1; columnIndex < firstRow.length; columnIndex++) {
    if (firstRow[columnIndex] === undefined) {
      const firstTypeValue = rows.find((row) => row[columnIndex] !== undefined);

      const value = firstTypeValue?.[columnIndex];
      if (value !== undefined) {
        firstRow[columnIndex] = value;
        firstRow[columnIndex + 2] = POINT_SIZE_0;
        firstRow[columnIndex + 3] = firstTypeValue?.[columnIndex + 3];
      }
    }
  }

  for (let columnIndex = 1; columnIndex < lastRow.length; columnIndex++) {
    if (lastRow[columnIndex] === undefined && columnIndex % 4 !== 0) {

      for (let rowIndex = rows.length - 1; rowIndex >= 0; rowIndex--) {
        const row = rows[rowIndex];
        // Find last row that has a value (lastTypeValue);
        const value = row[columnIndex];
        if (value !== undefined) {
          lastRow[columnIndex] = value;
          lastRow[columnIndex + 1] = !IS_SOLID_LINE;
          lastRow[columnIndex + 2] = POINT_SIZE_0;
          lastRow[columnIndex + 3] = row[columnIndex + 3];
          break;
        }
      }
    }
  }

  if (isBefore(new Date(), dateObj)) {
    chartData.push([
      new Date(),
      ...NUMBER_METRICS.map((label) => getChartLineValues(accumulatedValues[label], !IS_SOLID_LINE)).flat(),
    ] as ChartValue);
  }

  return chartData;
};

type TargetDirection = 1 | -1;

export const getChangeOfDirection = function (chartData: ChartDataTable, targetDirection: TargetDirection): number {
  if (chartData.length < 2) {
    return targetDirection;
  }

  const firstValue = chartData[1];
  const lastValue = chartData[chartData.length - 1];

  const valueField = 7;
  let direction = 0;
  const firstValueActual = Number(firstValue[valueField]);
  const lastValueActual = Number(lastValue[valueField]);

  if (firstValueActual < lastValueActual) {
    direction = 1 * targetDirection;
  } else if (firstValueActual > lastValueActual) {
    direction = -1 * targetDirection;
  }

  return direction;
};
